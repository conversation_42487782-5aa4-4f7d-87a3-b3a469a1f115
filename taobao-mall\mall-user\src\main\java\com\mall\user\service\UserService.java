package com.mall.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mall.user.dto.UserLoginDTO;
import com.mall.user.dto.UserRegisterDTO;
import com.mall.user.entity.User;
import com.mall.user.vo.UserLoginVO;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {
    
    /**
     * 用户注册
     */
    void register(UserRegisterDTO registerDTO);
    
    /**
     * 用户登录
     */
    UserLoginVO login(UserLoginDTO loginDTO);
    
    /**
     * 根据用户名查询用户
     */
    User getByUsername(String username);
    
    /**
     * 根据手机号查询用户
     */
    User getByPhone(String phone);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);
    
    /**
     * 更新最后登录信息
     */
    void updateLastLoginInfo(Long userId, String loginIp);
}

package com.mall.order.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mall.common.dto.PageResult;
import com.mall.common.exception.BusinessException;
import com.mall.common.result.Result;
import com.mall.common.result.ResultCode;
import com.mall.order.dto.OrderCreateDTO;
import com.mall.order.dto.OrderQueryDTO;
import com.mall.order.entity.Order;
import com.mall.order.entity.OrderItem;
import com.mall.order.feign.ProductFeignClient;
import com.mall.order.mapper.OrderMapper;
import com.mall.order.service.OrderItemService;
import com.mall.order.service.OrderService;
import com.mall.order.vo.OrderVO;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单服务实现类
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {
    
    @Autowired
    private OrderItemService orderItemService;
    
    @Autowired
    private ProductFeignClient productFeignClient;
    
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public OrderVO createOrder(Long userId, OrderCreateDTO createDTO) {
        // 1. 验证商品信息并计算金额
        List<OrderItem> orderItems = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (OrderCreateDTO.OrderItemDTO itemDTO : createDTO.getItems()) {
            // 查询商品信息
            Result<ProductFeignClient.ProductInfo> productResult = 
                productFeignClient.getProductById(itemDTO.getProductId());
            
            if (!productResult.isSuccess() || productResult.getData() == null) {
                throw new BusinessException(ResultCode.PRODUCT_NOT_EXIST);
            }
            
            ProductFeignClient.ProductInfo product = productResult.getData();
            
            // 检查商品状态
            if (product.getStatus() != 1) {
                throw new BusinessException(ResultCode.PRODUCT_NOT_EXIST, 
                    "商品【" + product.getName() + "】已下架");
            }
            
            // 检查库存
            if (product.getStock() < itemDTO.getQuantity()) {
                throw new BusinessException(ResultCode.PRODUCT_STOCK_NOT_ENOUGH, 
                    "商品【" + product.getName() + "】库存不足");
            }
            
            // 创建订单明细
            OrderItem orderItem = new OrderItem();
            orderItem.setProductId(product.getId());
            orderItem.setProductName(product.getName());
            orderItem.setProductImage(product.getMainImage());
            orderItem.setProductPrice(product.getPrice());
            orderItem.setQuantity(itemDTO.getQuantity());
            orderItem.setTotalAmount(product.getPrice().multiply(new BigDecimal(itemDTO.getQuantity())));
            
            orderItems.add(orderItem);
            totalAmount = totalAmount.add(orderItem.getTotalAmount());
        }
        
        // 2. 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setMerchantId(createDTO.getMerchantId());
        order.setStatus(0); // 待付款
        order.setOrderType(0); // 普通订单
        order.setProductAmount(totalAmount);
        order.setFreightAmount(BigDecimal.ZERO); // 暂时免运费
        order.setDiscountAmount(BigDecimal.ZERO); // 暂时无优惠
        order.setPayAmount(totalAmount);
        order.setPayType(createDTO.getPayType());
        order.setRemark(createDTO.getRemark());
        
        // TODO: 根据addressId设置收货信息
        order.setReceiverName("测试收货人");
        order.setReceiverPhone("13800138000");
        order.setReceiverAddress("测试收货地址");
        
        save(order);
        
        // 3. 保存订单明细
        for (OrderItem orderItem : orderItems) {
            orderItem.setOrderId(order.getId());
        }
        orderItemService.saveBatch(orderItems);
        
        // 4. 扣减库存
        for (OrderCreateDTO.OrderItemDTO itemDTO : createDTO.getItems()) {
            Result<Boolean> stockResult = productFeignClient.updateStock(
                itemDTO.getProductId(), -itemDTO.getQuantity());
            
            if (!stockResult.isSuccess() || !stockResult.getData()) {
                throw new BusinessException(ResultCode.INVENTORY_LOCK_FAILED);
            }
        }
        
        log.info("订单创建成功，订单号: {}", order.getOrderNo());
        
        // 5. 返回订单信息
        return getOrderByNo(order.getOrderNo());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payOrder(Long userId, String orderNo, Integer payType) {
        Order order = getOrderByOrderNo(orderNo);
        
        // 验证订单状态
        if (order.getStatus() != 0) {
            throw new BusinessException(ResultCode.ORDER_STATUS_ERROR, "订单状态错误");
        }
        
        // 验证用户权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "无权限操作此订单");
        }
        
        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setStatus(1); // 待发货
        updateOrder.setPayType(payType);
        updateOrder.setPayTime(LocalDateTime.now());
        
        boolean result = updateById(updateOrder);
        
        if (result) {
            // 增加商品销量
            List<OrderItem> orderItems = orderItemService.getByOrderId(order.getId());
            for (OrderItem item : orderItems) {
                productFeignClient.increaseSales(item.getProductId(), item.getQuantity());
            }
            
            log.info("订单支付成功，订单号: {}", orderNo);
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(Long userId, String orderNo) {
        Order order = getOrderByOrderNo(orderNo);
        
        // 验证订单状态（只有待付款状态可以取消）
        if (order.getStatus() != 0) {
            throw new BusinessException(ResultCode.ORDER_CANNOT_CANCEL);
        }
        
        // 验证用户权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "无权限操作此订单");
        }
        
        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setStatus(5); // 已取消
        updateOrder.setCancelTime(LocalDateTime.now());
        
        boolean result = updateById(updateOrder);
        
        if (result) {
            // 恢复库存
            List<OrderItem> orderItems = orderItemService.getByOrderId(order.getId());
            for (OrderItem item : orderItems) {
                productFeignClient.updateStock(item.getProductId(), item.getQuantity());
            }
            
            log.info("订单取消成功，订单号: {}", orderNo);
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmReceive(Long userId, String orderNo) {
        Order order = getOrderByOrderNo(orderNo);
        
        // 验证订单状态（只有待收货状态可以确认收货）
        if (order.getStatus() != 2) {
            throw new BusinessException(ResultCode.ORDER_STATUS_ERROR, "订单状态错误");
        }
        
        // 验证用户权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "无权限操作此订单");
        }
        
        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setStatus(3); // 待评价
        updateOrder.setReceiveTime(LocalDateTime.now());
        
        boolean result = updateById(updateOrder);
        
        if (result) {
            log.info("订单确认收货成功，订单号: {}", orderNo);
        }
        
        return result;
    }
    
    @Override
    public PageResult<OrderVO> getUserOrders(Long userId, OrderQueryDTO queryDTO) {
        Page<Order> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<Order>()
                .eq(Order::getUserId, userId)
                .eq(queryDTO.getStatus() != null, Order::getStatus, queryDTO.getStatus())
                .like(StrUtil.isNotBlank(queryDTO.getOrderNo()), Order::getOrderNo, queryDTO.getOrderNo())
                .orderByDesc(Order::getCreateTime);
        
        IPage<Order> result = page(page, wrapper);
        
        List<OrderVO> orderVOs = new ArrayList<>();
        for (Order order : result.getRecords()) {
            OrderVO orderVO = convertToOrderVO(order);
            orderVOs.add(orderVO);
        }
        
        return new PageResult<>(orderVOs, result.getTotal(), result.getCurrent(), result.getSize());
    }
    
    @Override
    public OrderVO getOrderByNo(String orderNo) {
        Order order = getOrderByOrderNo(orderNo);
        return convertToOrderVO(order);
    }
    
    @Override
    public OrderVO getUserOrderByNo(Long userId, String orderNo) {
        Order order = getOrderByOrderNo(orderNo);
        
        // 验证用户权限
        if (!order.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "无权限查看此订单");
        }
        
        return convertToOrderVO(order);
    }
    
    @Override
    public void handleOrderTimeout(String orderNo) {
        // TODO: 实现订单超时取消逻辑
        log.info("处理订单超时取消，订单号: {}", orderNo);
    }
    
    @Override
    public void handleAutoConfirmReceive(String orderNo) {
        // TODO: 实现订单自动确认收货逻辑
        log.info("处理订单自动确认收货，订单号: {}", orderNo);
    }
    
    /**
     * 根据订单号查询订单
     */
    private Order getOrderByOrderNo(String orderNo) {
        Order order = getOne(new LambdaQueryWrapper<Order>()
                .eq(Order::getOrderNo, orderNo)
                .eq(Order::getDeleted, 0));
        
        if (order == null) {
            throw new BusinessException(ResultCode.ORDER_NOT_EXIST);
        }
        
        return order;
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() + IdUtil.randomLong(1000, 9999);
    }
    
    /**
     * 转换为OrderVO
     */
    private OrderVO convertToOrderVO(Order order) {
        OrderVO orderVO = new OrderVO();
        orderVO.setId(order.getId());
        orderVO.setOrderNo(order.getOrderNo());
        orderVO.setUserId(order.getUserId());
        orderVO.setMerchantId(order.getMerchantId());
        orderVO.setStatus(order.getStatus());
        orderVO.setStatusDesc(getStatusDesc(order.getStatus()));
        orderVO.setOrderType(order.getOrderType());
        orderVO.setProductAmount(order.getProductAmount());
        orderVO.setFreightAmount(order.getFreightAmount());
        orderVO.setDiscountAmount(order.getDiscountAmount());
        orderVO.setPayAmount(order.getPayAmount());
        orderVO.setPayType(order.getPayType());
        orderVO.setPayTypeDesc(getPayTypeDesc(order.getPayType()));
        orderVO.setPayTime(order.getPayTime());
        orderVO.setShipTime(order.getShipTime());
        orderVO.setReceiveTime(order.getReceiveTime());
        orderVO.setFinishTime(order.getFinishTime());
        orderVO.setCancelTime(order.getCancelTime());
        orderVO.setReceiverName(order.getReceiverName());
        orderVO.setReceiverPhone(order.getReceiverPhone());
        orderVO.setReceiverAddress(order.getReceiverAddress());
        orderVO.setLogisticsCompany(order.getLogisticsCompany());
        orderVO.setLogisticsNo(order.getLogisticsNo());
        orderVO.setRemark(order.getRemark());
        orderVO.setCreateTime(order.getCreateTime());
        
        // 查询订单明细
        List<OrderItem> orderItems = orderItemService.getByOrderId(order.getId());
        List<OrderVO.OrderItemVO> itemVOs = new ArrayList<>();
        for (OrderItem item : orderItems) {
            OrderVO.OrderItemVO itemVO = new OrderVO.OrderItemVO();
            itemVO.setProductId(item.getProductId());
            itemVO.setProductName(item.getProductName());
            itemVO.setProductImage(item.getProductImage());
            itemVO.setProductPrice(item.getProductPrice());
            itemVO.setQuantity(item.getQuantity());
            itemVO.setTotalAmount(item.getTotalAmount());
            itemVOs.add(itemVO);
        }
        orderVO.setItems(itemVOs);
        
        return orderVO;
    }
    
    /**
     * 获取订单状态描述
     */
    private String getStatusDesc(Integer status) {
        switch (status) {
            case 0: return "待付款";
            case 1: return "待发货";
            case 2: return "待收货";
            case 3: return "待评价";
            case 4: return "已完成";
            case 5: return "已取消";
            case 6: return "已退款";
            default: return "未知状态";
        }
    }
    
    /**
     * 获取支付方式描述
     */
    private String getPayTypeDesc(Integer payType) {
        if (payType == null) return "";
        switch (payType) {
            case 1: return "支付宝";
            case 2: return "微信支付";
            case 3: return "银联支付";
            default: return "未知支付方式";
        }
    }
}

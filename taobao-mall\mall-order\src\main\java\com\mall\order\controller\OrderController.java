package com.mall.order.controller;

import com.mall.common.dto.PageResult;
import com.mall.common.result.Result;
import com.mall.order.dto.OrderCreateDTO;
import com.mall.order.dto.OrderQueryDTO;
import com.mall.order.service.OrderService;
import com.mall.order.vo.OrderVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/order")
@Tag(name = "订单管理", description = "订单创建、支付、查询等接口")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    /**
     * 创建订单
     */
    @PostMapping("/create")
    @Operation(summary = "创建订单")
    public Result<OrderVO> createOrder(@RequestHeader("X-User-Id") Long userId,
                                       @Validated @RequestBody OrderCreateDTO createDTO) {
        OrderVO orderVO = orderService.createOrder(userId, createDTO);
        return Result.success(orderVO);
    }
    
    /**
     * 支付订单
     */
    @PostMapping("/pay")
    @Operation(summary = "支付订单")
    public Result<Boolean> payOrder(@RequestHeader("X-User-Id") Long userId,
                                    @Parameter(description = "订单号") @RequestParam String orderNo,
                                    @Parameter(description = "支付方式") @RequestParam Integer payType) {
        boolean result = orderService.payOrder(userId, orderNo, payType);
        return Result.success(result);
    }
    
    /**
     * 取消订单
     */
    @PostMapping("/cancel")
    @Operation(summary = "取消订单")
    public Result<Boolean> cancelOrder(@RequestHeader("X-User-Id") Long userId,
                                       @Parameter(description = "订单号") @RequestParam String orderNo) {
        boolean result = orderService.cancelOrder(userId, orderNo);
        return Result.success(result);
    }
    
    /**
     * 确认收货
     */
    @PostMapping("/confirm-receive")
    @Operation(summary = "确认收货")
    public Result<Boolean> confirmReceive(@RequestHeader("X-User-Id") Long userId,
                                          @Parameter(description = "订单号") @RequestParam String orderNo) {
        boolean result = orderService.confirmReceive(userId, orderNo);
        return Result.success(result);
    }
    
    /**
     * 分页查询用户订单
     */
    @GetMapping("/list")
    @Operation(summary = "分页查询用户订单")
    public Result<PageResult<OrderVO>> getUserOrders(@RequestHeader("X-User-Id") Long userId,
                                                      @Validated OrderQueryDTO queryDTO) {
        PageResult<OrderVO> result = orderService.getUserOrders(userId, queryDTO);
        return Result.success(result);
    }
    
    /**
     * 查询订单详情
     */
    @GetMapping("/detail/{orderNo}")
    @Operation(summary = "查询订单详情")
    public Result<OrderVO> getOrderDetail(@RequestHeader("X-User-Id") Long userId,
                                          @Parameter(description = "订单号") @PathVariable String orderNo) {
        OrderVO orderVO = orderService.getUserOrderByNo(userId, orderNo);
        return Result.success(orderVO);
    }
}

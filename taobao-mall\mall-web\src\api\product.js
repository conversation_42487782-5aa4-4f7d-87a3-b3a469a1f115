import request from '@/utils/request'

// 获取商品列表
export function getProductList(params) {
  return request({
    url: '/product/list',
    method: 'get',
    params
  })
}

// 获取商品详情
export function getProductDetail(id) {
  return request({
    url: `/product/detail/${id}`,
    method: 'get'
  })
}

// 获取推荐商品
export function getRecommendProducts(limit = 10) {
  return request({
    url: '/product/recommend',
    method: 'get',
    params: { limit }
  })
}

// 获取热销商品
export function getHotProducts(limit = 10) {
  return request({
    url: '/product/hot',
    method: 'get',
    params: { limit }
  })
}

// 获取新品
export function getNewProducts(limit = 10) {
  return request({
    url: '/product/new',
    method: 'get',
    params: { limit }
  })
}

// 根据分类获取商品
export function getProductsByCategory(categoryId, limit = 20) {
  return request({
    url: `/product/category/${categoryId}`,
    method: 'get',
    params: { limit }
  })
}

// 搜索商品
export function searchProducts(params) {
  return request({
    url: '/product/search',
    method: 'get',
    params
  })
}

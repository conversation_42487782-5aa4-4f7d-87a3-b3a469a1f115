-- 创建商品数据库
CREATE DATABASE IF NOT EXISTS `mall_product` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `mall_product`;

-- 商品分类表
DROP TABLE IF EXISTS `t_product_category`;
CREATE TABLE `t_product_category` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父分类ID，0表示一级分类',
  `level` tinyint NOT NULL DEFAULT '1' COMMENT '分类级别 1-一级 2-二级 3-三级',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) DEFAULT NULL COMMENT '分类图片',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `sort_order` int DEFAULT '0' COMMENT '排序值',
  `status` tinyint DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-已删除',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 品牌表
DROP TABLE IF EXISTS `t_brand`;
CREATE TABLE `t_brand` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) DEFAULT NULL COMMENT '品牌Logo',
  `description` varchar(500) DEFAULT NULL COMMENT '品牌描述',
  `sort_order` int DEFAULT '0' COMMENT '排序值',
  `status` tinyint DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-已删除',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='品牌表';

-- 商品表
DROP TABLE IF EXISTS `t_product`;
CREATE TABLE `t_product` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `name` varchar(200) NOT NULL COMMENT '商品名称',
  `subtitle` varchar(300) DEFAULT NULL COMMENT '商品副标题',
  `description` varchar(500) DEFAULT NULL COMMENT '商品描述',
  `category_id` bigint NOT NULL COMMENT '商品分类ID',
  `merchant_id` bigint NOT NULL COMMENT '商家ID',
  `brand_id` bigint DEFAULT NULL COMMENT '品牌ID',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `market_price` decimal(10,2) DEFAULT NULL COMMENT '市场价格',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价格',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `sales` int NOT NULL DEFAULT '0' COMMENT '销量',
  `main_image` varchar(255) DEFAULT NULL COMMENT '商品主图',
  `images` text COMMENT '商品图片（多张，逗号分隔）',
  `detail` longtext COMMENT '商品详情',
  `status` tinyint DEFAULT '0' COMMENT '商品状态 0-下架 1-上架',
  `weight` int DEFAULT NULL COMMENT '商品重量（克）',
  `volume` int DEFAULT NULL COMMENT '商品体积（立方厘米）',
  `is_recommend` tinyint DEFAULT '0' COMMENT '是否推荐 0-否 1-是',
  `is_hot` tinyint DEFAULT '0' COMMENT '是否热销 0-否 1-是',
  `is_new` tinyint DEFAULT '0' COMMENT '是否新品 0-否 1-是',
  `sort_order` int DEFAULT '0' COMMENT '排序值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-已删除',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_brand_id` (`brand_id`),
  KEY `idx_price` (`price`),
  KEY `idx_status` (`status`),
  KEY `idx_is_recommend` (`is_recommend`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_is_new` (`is_new`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 插入测试数据
-- 分类数据
INSERT INTO `t_product_category` (`id`, `name`, `parent_id`, `level`, `sort_order`, `status`) VALUES
(1, '电子产品', 0, 1, 1, 1),
(2, '服装鞋帽', 0, 1, 2, 1),
(3, '家居用品', 0, 1, 3, 1),
(11, '手机数码', 1, 2, 1, 1),
(12, '电脑办公', 1, 2, 2, 1),
(21, '男装', 2, 2, 1, 1),
(22, '女装', 2, 2, 2, 1),
(31, '家具', 3, 2, 1, 1),
(32, '家电', 3, 2, 2, 1);

-- 品牌数据
INSERT INTO `t_brand` (`id`, `name`, `description`, `sort_order`, `status`) VALUES
(1, '苹果', 'Apple Inc.', 1, 1),
(2, '华为', 'HUAWEI', 2, 1),
(3, '小米', 'Xiaomi', 3, 1),
(4, '耐克', 'Nike', 4, 1),
(5, '阿迪达斯', 'Adidas', 5, 1);

-- 商品数据
INSERT INTO `t_product` (`id`, `name`, `subtitle`, `description`, `category_id`, `merchant_id`, `brand_id`, `price`, `market_price`, `stock`, `sales`, `main_image`, `status`, `is_recommend`, `is_hot`, `is_new`, `sort_order`) VALUES
(1, 'iPhone 15 Pro', '全新A17 Pro芯片，钛金属设计', '苹果最新旗舰手机', 11, 1, 1, 7999.00, 8999.00, 100, 50, '/images/iphone15pro.jpg', 1, 1, 1, 1, 1),
(2, 'MacBook Pro 14英寸', 'M3芯片，专业级性能', '苹果专业笔记本电脑', 12, 1, 1, 14999.00, 16999.00, 50, 20, '/images/macbookpro14.jpg', 1, 1, 0, 1, 2),
(3, '华为Mate 60 Pro', '卫星通话，麒麟9000S', '华为旗舰手机', 11, 2, 2, 6999.00, 7999.00, 80, 30, '/images/mate60pro.jpg', 1, 0, 1, 0, 3),
(4, '小米14 Ultra', '徕卡影像，骁龙8 Gen3', '小米影像旗舰', 11, 3, 3, 5999.00, 6999.00, 120, 80, '/images/mi14ultra.jpg', 1, 1, 1, 1, 4),
(5, 'Nike Air Max 270', '气垫运动鞋', '舒适透气的运动鞋', 21, 4, 4, 899.00, 1299.00, 200, 150, '/images/airmax270.jpg', 1, 0, 1, 0, 5);

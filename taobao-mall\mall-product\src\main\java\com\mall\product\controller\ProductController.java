package com.mall.product.controller;

import com.mall.common.dto.PageResult;
import com.mall.common.result.Result;
import com.mall.product.dto.ProductQueryDTO;
import com.mall.product.service.ProductService;
import com.mall.product.vo.ProductVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品控制器
 */
@Slf4j
@RestController
@RequestMapping("/product")
@Tag(name = "商品管理", description = "商品查询、详情等接口")
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    /**
     * 分页查询商品列表
     */
    @GetMapping("/list")
    @Operation(summary = "分页查询商品列表")
    public Result<PageResult<ProductVO>> getProductList(@Validated ProductQueryDTO queryDTO) {
        PageResult<ProductVO> result = productService.getProductPage(queryDTO);
        return Result.success(result);
    }
    
    /**
     * 根据ID查询商品详情
     */
    @GetMapping("/detail/{id}")
    @Operation(summary = "查询商品详情")
    public Result<ProductVO> getProductDetail(
            @Parameter(description = "商品ID") @PathVariable Long id) {
        ProductVO productVO = productService.getProductById(id);
        return Result.success(productVO);
    }
    
    /**
     * 获取推荐商品列表
     */
    @GetMapping("/recommend")
    @Operation(summary = "获取推荐商品列表")
    public Result<List<ProductVO>> getRecommendProducts(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        List<ProductVO> products = productService.getRecommendProducts(limit);
        return Result.success(products);
    }
    
    /**
     * 获取热销商品列表
     */
    @GetMapping("/hot")
    @Operation(summary = "获取热销商品列表")
    public Result<List<ProductVO>> getHotProducts(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        List<ProductVO> products = productService.getHotProducts(limit);
        return Result.success(products);
    }
    
    /**
     * 获取新品列表
     */
    @GetMapping("/new")
    @Operation(summary = "获取新品列表")
    public Result<List<ProductVO>> getNewProducts(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        List<ProductVO> products = productService.getNewProducts(limit);
        return Result.success(products);
    }
    
    /**
     * 根据分类获取商品列表
     */
    @GetMapping("/category/{categoryId}")
    @Operation(summary = "根据分类获取商品列表")
    public Result<List<ProductVO>> getProductsByCategory(
            @Parameter(description = "分类ID") @PathVariable Long categoryId,
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "20") Integer limit) {
        List<ProductVO> products = productService.getProductsByCategory(categoryId, limit);
        return Result.success(products);
    }
    
    /**
     * 搜索商品
     */
    @GetMapping("/search")
    @Operation(summary = "搜索商品")
    public Result<PageResult<ProductVO>> searchProducts(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Validated ProductQueryDTO queryDTO) {
        PageResult<ProductVO> result = productService.searchProducts(keyword, queryDTO);
        return Result.success(result);
    }
}

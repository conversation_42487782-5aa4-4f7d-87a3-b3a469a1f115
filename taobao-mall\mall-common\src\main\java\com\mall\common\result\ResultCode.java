package com.mall.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回状态码枚举
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    
    // 用户相关
    USER_NOT_EXIST(1001, "用户不存在"),
    USER_ALREADY_EXIST(1002, "用户已存在"),
    USER_PASSWORD_ERROR(1003, "密码错误"),
    USER_DISABLED(1004, "用户已被禁用"),
    USER_LOGIN_EXPIRED(1005, "登录已过期"),
    
    // 商品相关
    PRODUCT_NOT_EXIST(2001, "商品不存在"),
    PRODUCT_STOCK_NOT_ENOUGH(2002, "商品库存不足"),
    PRODUCT_ALREADY_EXIST(2003, "商品已存在"),
    PRODUCT_CATEGORY_NOT_EXIST(2004, "商品分类不存在"),
    
    // 订单相关
    ORDER_NOT_EXIST(3001, "订单不存在"),
    ORDER_STATUS_ERROR(3002, "订单状态错误"),
    ORDER_CANNOT_CANCEL(3003, "订单无法取消"),
    ORDER_ALREADY_PAID(3004, "订单已支付"),
    
    // 购物车相关
    CART_ITEM_NOT_EXIST(4001, "购物车商品不存在"),
    CART_ITEM_ALREADY_EXIST(4002, "购物车商品已存在"),
    
    // 支付相关
    PAYMENT_ERROR(5001, "支付失败"),
    PAYMENT_TIMEOUT(5002, "支付超时"),
    PAYMENT_AMOUNT_ERROR(5003, "支付金额错误"),
    
    // 商家相关
    MERCHANT_NOT_EXIST(6001, "商家不存在"),
    MERCHANT_ALREADY_EXIST(6002, "商家已存在"),
    MERCHANT_DISABLED(6003, "商家已被禁用"),
    
    // 库存相关
    INVENTORY_NOT_ENOUGH(7001, "库存不足"),
    INVENTORY_LOCK_FAILED(7002, "库存锁定失败"),
    
    // 物流相关
    LOGISTICS_NOT_EXIST(8001, "物流信息不存在"),
    LOGISTICS_STATUS_ERROR(8002, "物流状态错误");
    
    private final Integer code;
    private final String message;
}

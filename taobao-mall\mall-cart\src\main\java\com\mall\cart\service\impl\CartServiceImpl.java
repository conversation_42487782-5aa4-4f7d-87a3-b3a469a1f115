package com.mall.cart.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.mall.cart.dto.CartItemDTO;
import com.mall.cart.feign.ProductFeignClient;
import com.mall.cart.service.CartService;
import com.mall.cart.vo.CartItemVO;
import com.mall.cart.vo.CartVO;
import com.mall.common.exception.BusinessException;
import com.mall.common.result.Result;
import com.mall.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 购物车服务实现类
 */
@Slf4j
@Service
public class CartServiceImpl implements CartService {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Autowired
    private ProductFeignClient productFeignClient;
    
    private static final String CART_KEY_PREFIX = "cart:";
    private static final String CART_SELECTED_KEY_PREFIX = "cart:selected:";
    private static final long CART_EXPIRE_TIME = 30; // 30天过期
    
    @Override
    public void addToCart(Long userId, CartItemDTO cartItemDTO) {
        // 验证商品信息
        Result<ProductFeignClient.ProductInfo> productResult = 
            productFeignClient.getProductById(cartItemDTO.getProductId());
        
        if (!productResult.isSuccess() || productResult.getData() == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_EXIST);
        }
        
        ProductFeignClient.ProductInfo product = productResult.getData();
        if (product.getStatus() != 1) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_EXIST, "商品已下架");
        }
        
        String cartKey = CART_KEY_PREFIX + userId;
        String productId = String.valueOf(cartItemDTO.getProductId());
        
        // 检查购物车中是否已存在该商品
        String existingItem = redisTemplate.opsForHash().get(cartKey, productId).toString();
        
        CartItemVO cartItem;
        if (existingItem != null) {
            // 商品已存在，更新数量
            cartItem = JSON.parseObject(existingItem, CartItemVO.class);
            cartItem.setQuantity(cartItem.getQuantity() + cartItemDTO.getQuantity());
        } else {
            // 新商品，创建购物车项
            cartItem = new CartItemVO();
            cartItem.setProductId(product.getId());
            cartItem.setProductName(product.getName());
            cartItem.setProductImage(product.getMainImage());
            cartItem.setProductPrice(product.getPrice());
            cartItem.setQuantity(cartItemDTO.getQuantity());
            cartItem.setSelected(true);
            cartItem.setProductStatus(product.getStatus());
            cartItem.setStock(product.getStock());
        }
        
        // 检查库存
        if (cartItem.getQuantity() > product.getStock()) {
            throw new BusinessException(ResultCode.PRODUCT_STOCK_NOT_ENOUGH);
        }
        
        // 计算小计
        cartItem.setTotalAmount(cartItem.getProductPrice().multiply(new BigDecimal(cartItem.getQuantity())));
        
        // 保存到Redis
        redisTemplate.opsForHash().put(cartKey, productId, JSON.toJSONString(cartItem));
        redisTemplate.expire(cartKey, CART_EXPIRE_TIME, TimeUnit.DAYS);
        
        log.info("用户{}添加商品{}到购物车成功", userId, cartItemDTO.getProductId());
    }
    
    @Override
    public void updateCartItem(Long userId, Long productId, Integer quantity) {
        String cartKey = CART_KEY_PREFIX + userId;
        String productIdStr = String.valueOf(productId);
        
        Object itemObj = redisTemplate.opsForHash().get(cartKey, productIdStr);
        if (itemObj == null) {
            throw new BusinessException(ResultCode.CART_ITEM_NOT_EXIST);
        }
        
        CartItemVO cartItem = JSON.parseObject(itemObj.toString(), CartItemVO.class);
        
        if (quantity <= 0) {
            // 数量为0或负数，删除商品
            removeFromCart(userId, productId);
            return;
        }
        
        // 检查库存
        if (quantity > cartItem.getStock()) {
            throw new BusinessException(ResultCode.PRODUCT_STOCK_NOT_ENOUGH);
        }
        
        cartItem.setQuantity(quantity);
        cartItem.setTotalAmount(cartItem.getProductPrice().multiply(new BigDecimal(quantity)));
        
        redisTemplate.opsForHash().put(cartKey, productIdStr, JSON.toJSONString(cartItem));
        redisTemplate.expire(cartKey, CART_EXPIRE_TIME, TimeUnit.DAYS);
        
        log.info("用户{}更新购物车商品{}数量为{}", userId, productId, quantity);
    }
    
    @Override
    public void removeFromCart(Long userId, Long productId) {
        String cartKey = CART_KEY_PREFIX + userId;
        String selectedKey = CART_SELECTED_KEY_PREFIX + userId;
        String productIdStr = String.valueOf(productId);
        
        redisTemplate.opsForHash().delete(cartKey, productIdStr);
        redisTemplate.opsForSet().remove(selectedKey, productIdStr);
        
        log.info("用户{}删除购物车商品{}", userId, productId);
    }
    
    @Override
    public void removeFromCart(Long userId, List<Long> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            return;
        }
        
        String cartKey = CART_KEY_PREFIX + userId;
        String selectedKey = CART_SELECTED_KEY_PREFIX + userId;
        
        String[] productIdStrs = productIds.stream()
                .map(String::valueOf)
                .toArray(String[]::new);
        
        redisTemplate.opsForHash().delete(cartKey, (Object[]) productIdStrs);
        redisTemplate.opsForSet().remove(selectedKey, (Object[]) productIdStrs);
        
        log.info("用户{}批量删除购物车商品{}", userId, productIds);
    }
    
    @Override
    public void clearCart(Long userId) {
        String cartKey = CART_KEY_PREFIX + userId;
        String selectedKey = CART_SELECTED_KEY_PREFIX + userId;
        
        redisTemplate.delete(cartKey);
        redisTemplate.delete(selectedKey);
        
        log.info("用户{}清空购物车", userId);
    }
    
    @Override
    public CartVO getUserCart(Long userId) {
        String cartKey = CART_KEY_PREFIX + userId;
        String selectedKey = CART_SELECTED_KEY_PREFIX + userId;
        
        Map<Object, Object> cartItems = redisTemplate.opsForHash().entries(cartKey);
        
        CartVO cartVO = new CartVO();
        cartVO.setUserId(userId);
        
        if (cartItems.isEmpty()) {
            cartVO.setItems(new ArrayList<>());
            cartVO.setTotalQuantity(0);
            cartVO.setTotalAmount(BigDecimal.ZERO);
            cartVO.setSelectedQuantity(0);
            cartVO.setSelectedAmount(BigDecimal.ZERO);
            return cartVO;
        }
        
        // 获取选中的商品ID
        Set<Object> selectedProductIds = redisTemplate.opsForSet().members(selectedKey);
        
        List<CartItemVO> items = new ArrayList<>();
        int totalQuantity = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        int selectedQuantity = 0;
        BigDecimal selectedAmount = BigDecimal.ZERO;
        
        for (Map.Entry<Object, Object> entry : cartItems.entrySet()) {
            String productId = entry.getKey().toString();
            CartItemVO item = JSON.parseObject(entry.getValue().toString(), CartItemVO.class);
            
            // 设置选中状态
            boolean isSelected = selectedProductIds != null && selectedProductIds.contains(productId);
            item.setSelected(isSelected);
            
            items.add(item);
            totalQuantity += item.getQuantity();
            totalAmount = totalAmount.add(item.getTotalAmount());
            
            if (isSelected) {
                selectedQuantity += item.getQuantity();
                selectedAmount = selectedAmount.add(item.getTotalAmount());
            }
        }
        
        cartVO.setItems(items);
        cartVO.setTotalQuantity(totalQuantity);
        cartVO.setTotalAmount(totalAmount);
        cartVO.setSelectedQuantity(selectedQuantity);
        cartVO.setSelectedAmount(selectedAmount);
        
        return cartVO;
    }
    
    @Override
    public Integer getCartItemCount(Long userId) {
        String cartKey = CART_KEY_PREFIX + userId;
        return redisTemplate.opsForHash().size(cartKey).intValue();
    }
    
    @Override
    public void selectCartItem(Long userId, Long productId, Boolean selected) {
        String selectedKey = CART_SELECTED_KEY_PREFIX + userId;
        String productIdStr = String.valueOf(productId);
        
        if (selected) {
            redisTemplate.opsForSet().add(selectedKey, productIdStr);
        } else {
            redisTemplate.opsForSet().remove(selectedKey, productIdStr);
        }
        
        redisTemplate.expire(selectedKey, CART_EXPIRE_TIME, TimeUnit.DAYS);
        
        log.info("用户{}{}选中购物车商品{}", userId, selected ? "" : "取消", productId);
    }
    
    @Override
    public void selectAllCartItems(Long userId, Boolean selected) {
        String cartKey = CART_KEY_PREFIX + userId;
        String selectedKey = CART_SELECTED_KEY_PREFIX + userId;
        
        if (selected) {
            // 全选：将所有商品ID添加到选中集合
            Set<Object> productIds = redisTemplate.opsForHash().keys(cartKey);
            if (!productIds.isEmpty()) {
                redisTemplate.opsForSet().add(selectedKey, productIds.toArray());
                redisTemplate.expire(selectedKey, CART_EXPIRE_TIME, TimeUnit.DAYS);
            }
        } else {
            // 取消全选：清空选中集合
            redisTemplate.delete(selectedKey);
        }
        
        log.info("用户{}{}全选购物车商品", userId, selected ? "" : "取消");
    }
    
    @Override
    public List<CartItemVO> getSelectedCartItems(Long userId) {
        CartVO cartVO = getUserCart(userId);
        return cartVO.getItems().stream()
                .filter(CartItemVO::getSelected)
                .collect(Collectors.toList());
    }
}

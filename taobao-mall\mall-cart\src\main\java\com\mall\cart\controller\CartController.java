package com.mall.cart.controller;

import com.mall.cart.dto.CartItemDTO;
import com.mall.cart.service.CartService;
import com.mall.cart.vo.CartItemVO;
import com.mall.cart.vo.CartVO;
import com.mall.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 购物车控制器
 */
@Slf4j
@RestController
@RequestMapping("/cart")
@Tag(name = "购物车管理", description = "购物车增删改查等接口")
public class CartController {
    
    @Autowired
    private CartService cartService;
    
    /**
     * 添加商品到购物车
     */
    @PostMapping("/add")
    @Operation(summary = "添加商品到购物车")
    public Result<Void> addToCart(@RequestHeader("X-User-Id") Long userId,
                                  @Validated @RequestBody CartItemDTO cartItemDTO) {
        cartService.addToCart(userId, cartItemDTO);
        return Result.success("添加成功");
    }
    
    /**
     * 更新购物车商品数量
     */
    @PutMapping("/update")
    @Operation(summary = "更新购物车商品数量")
    public Result<Void> updateCartItem(@RequestHeader("X-User-Id") Long userId,
                                       @Parameter(description = "商品ID") @RequestParam Long productId,
                                       @Parameter(description = "数量") @RequestParam Integer quantity) {
        cartService.updateCartItem(userId, productId, quantity);
        return Result.success("更新成功");
    }
    
    /**
     * 删除购物车商品
     */
    @DeleteMapping("/remove/{productId}")
    @Operation(summary = "删除购物车商品")
    public Result<Void> removeFromCart(@RequestHeader("X-User-Id") Long userId,
                                       @Parameter(description = "商品ID") @PathVariable Long productId) {
        cartService.removeFromCart(userId, productId);
        return Result.success("删除成功");
    }
    
    /**
     * 批量删除购物车商品
     */
    @DeleteMapping("/remove/batch")
    @Operation(summary = "批量删除购物车商品")
    public Result<Void> removeFromCart(@RequestHeader("X-User-Id") Long userId,
                                       @Parameter(description = "商品ID列表") @RequestBody List<Long> productIds) {
        cartService.removeFromCart(userId, productIds);
        return Result.success("删除成功");
    }
    
    /**
     * 清空购物车
     */
    @DeleteMapping("/clear")
    @Operation(summary = "清空购物车")
    public Result<Void> clearCart(@RequestHeader("X-User-Id") Long userId) {
        cartService.clearCart(userId);
        return Result.success("清空成功");
    }
    
    /**
     * 获取用户购物车
     */
    @GetMapping("/list")
    @Operation(summary = "获取用户购物车")
    public Result<CartVO> getUserCart(@RequestHeader("X-User-Id") Long userId) {
        CartVO cartVO = cartService.getUserCart(userId);
        return Result.success(cartVO);
    }
    
    /**
     * 获取购物车商品数量
     */
    @GetMapping("/count")
    @Operation(summary = "获取购物车商品数量")
    public Result<Integer> getCartItemCount(@RequestHeader("X-User-Id") Long userId) {
        Integer count = cartService.getCartItemCount(userId);
        return Result.success(count);
    }
    
    /**
     * 选中/取消选中购物车商品
     */
    @PutMapping("/select")
    @Operation(summary = "选中/取消选中购物车商品")
    public Result<Void> selectCartItem(@RequestHeader("X-User-Id") Long userId,
                                       @Parameter(description = "商品ID") @RequestParam Long productId,
                                       @Parameter(description = "是否选中") @RequestParam Boolean selected) {
        cartService.selectCartItem(userId, productId, selected);
        return Result.success("操作成功");
    }
    
    /**
     * 全选/取消全选购物车商品
     */
    @PutMapping("/select/all")
    @Operation(summary = "全选/取消全选购物车商品")
    public Result<Void> selectAllCartItems(@RequestHeader("X-User-Id") Long userId,
                                           @Parameter(description = "是否全选") @RequestParam Boolean selected) {
        cartService.selectAllCartItems(userId, selected);
        return Result.success("操作成功");
    }
    
    /**
     * 获取选中的购物车商品
     */
    @GetMapping("/selected")
    @Operation(summary = "获取选中的购物车商品")
    public Result<List<CartItemVO>> getSelectedCartItems(@RequestHeader("X-User-Id") Long userId) {
        List<CartItemVO> items = cartService.getSelectedCartItems(userId);
        return Result.success(items);
    }
}

package com.mall.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mall.common.dto.PageResult;
import com.mall.product.dto.ProductQueryDTO;
import com.mall.product.entity.Product;
import com.mall.product.vo.ProductVO;

import java.util.List;

/**
 * 商品服务接口
 */
public interface ProductService extends IService<Product> {
    
    /**
     * 分页查询商品列表
     */
    PageResult<ProductVO> getProductPage(ProductQueryDTO queryDTO);
    
    /**
     * 根据ID查询商品详情
     */
    ProductVO getProductById(Long id);
    
    /**
     * 获取推荐商品列表
     */
    List<ProductVO> getRecommendProducts(Integer limit);
    
    /**
     * 获取热销商品列表
     */
    List<ProductVO> getHotProducts(Integer limit);
    
    /**
     * 获取新品列表
     */
    List<ProductVO> getNewProducts(Integer limit);
    
    /**
     * 根据分类ID获取商品列表
     */
    List<ProductVO> getProductsByCategory(Long categoryId, Integer limit);
    
    /**
     * 搜索商品
     */
    PageResult<ProductVO> searchProducts(String keyword, ProductQueryDTO queryDTO);
    
    /**
     * 更新商品库存
     */
    boolean updateStock(Long productId, Integer quantity);
    
    /**
     * 增加商品销量
     */
    boolean increaseSales(Long productId, Integer quantity);
}

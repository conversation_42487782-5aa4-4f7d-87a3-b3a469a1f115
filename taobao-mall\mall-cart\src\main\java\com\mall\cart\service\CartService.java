package com.mall.cart.service;

import com.mall.cart.dto.CartItemDTO;
import com.mall.cart.vo.CartItemVO;
import com.mall.cart.vo.CartVO;

import java.util.List;

/**
 * 购物车服务接口
 */
public interface CartService {
    
    /**
     * 添加商品到购物车
     */
    void addToCart(Long userId, CartItemDTO cartItemDTO);
    
    /**
     * 更新购物车商品数量
     */
    void updateCartItem(Long userId, Long productId, Integer quantity);
    
    /**
     * 删除购物车商品
     */
    void removeFromCart(Long userId, Long productId);
    
    /**
     * 批量删除购物车商品
     */
    void removeFromCart(Long userId, List<Long> productIds);
    
    /**
     * 清空购物车
     */
    void clearCart(Long userId);
    
    /**
     * 获取用户购物车
     */
    CartVO getUserCart(Long userId);
    
    /**
     * 获取购物车商品数量
     */
    Integer getCartItemCount(Long userId);
    
    /**
     * 选中/取消选中购物车商品
     */
    void selectCartItem(Long userId, Long productId, Boolean selected);
    
    /**
     * 全选/取消全选购物车商品
     */
    void selectAllCartItems(Long userId, Boolean selected);
    
    /**
     * 获取选中的购物车商品
     */
    List<CartItemVO> getSelectedCartItems(Long userId);
}

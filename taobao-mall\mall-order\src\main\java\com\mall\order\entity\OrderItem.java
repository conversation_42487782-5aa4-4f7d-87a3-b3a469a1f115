package com.mall.order.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mall.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订单明细实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_order_item")
public class OrderItem extends BaseEntity {
    
    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;
    
    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;
    
    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;
    
    /**
     * 商品图片
     */
    @TableField("product_image")
    private String productImage;
    
    /**
     * 商品价格
     */
    @TableField("product_price")
    private BigDecimal productPrice;
    
    /**
     * 购买数量
     */
    @TableField("quantity")
    private Integer quantity;
    
    /**
     * 小计金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;
}

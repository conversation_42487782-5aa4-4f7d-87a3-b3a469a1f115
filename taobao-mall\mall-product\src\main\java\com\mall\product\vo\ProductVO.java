package com.mall.product.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品VO
 */
@Data
public class ProductVO {
    
    /**
     * 商品ID
     */
    private Long id;
    
    /**
     * 商品名称
     */
    private String name;
    
    /**
     * 商品副标题
     */
    private String subtitle;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 商品分类ID
     */
    private Long categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 商家ID
     */
    private Long merchantId;
    
    /**
     * 商家名称
     */
    private String merchantName;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 商品价格
     */
    private BigDecimal price;
    
    /**
     * 市场价格
     */
    private BigDecimal marketPrice;
    
    /**
     * 库存数量
     */
    private Integer stock;
    
    /**
     * 销量
     */
    private Integer sales;
    
    /**
     * 商品主图
     */
    private String mainImage;
    
    /**
     * 商品图片列表
     */
    private List<String> imageList;
    
    /**
     * 商品详情
     */
    private String detail;
    
    /**
     * 商品状态 0-下架 1-上架
     */
    private Integer status;
    
    /**
     * 商品重量（克）
     */
    private Integer weight;
    
    /**
     * 商品体积（立方厘米）
     */
    private Integer volume;
    
    /**
     * 是否推荐 0-否 1-是
     */
    private Integer isRecommend;
    
    /**
     * 是否热销 0-否 1-是
     */
    private Integer isHot;
    
    /**
     * 是否新品 0-否 1-是
     */
    private Integer isNew;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mall.product.mapper.ProductMapper">

    <!-- 商品VO结果映射 -->
    <resultMap id="ProductVOMap" type="com.mall.product.vo.ProductVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="subtitle" property="subtitle"/>
        <result column="description" property="description"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="brand_id" property="brandId"/>
        <result column="brand_name" property="brandName"/>
        <result column="price" property="price"/>
        <result column="market_price" property="marketPrice"/>
        <result column="stock" property="stock"/>
        <result column="sales" property="sales"/>
        <result column="main_image" property="mainImage"/>
        <result column="images" property="images"/>
        <result column="detail" property="detail"/>
        <result column="status" property="status"/>
        <result column="weight" property="weight"/>
        <result column="volume" property="volume"/>
        <result column="is_recommend" property="isRecommend"/>
        <result column="is_hot" property="isHot"/>
        <result column="is_new" property="isNew"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseSelectColumns">
        p.id,
        p.name,
        p.subtitle,
        p.description,
        p.category_id,
        c.name as category_name,
        p.merchant_id,
        '' as merchant_name,
        p.brand_id,
        b.name as brand_name,
        p.price,
        p.market_price,
        p.stock,
        p.sales,
        p.main_image,
        p.images,
        p.detail,
        p.status,
        p.weight,
        p.volume,
        p.is_recommend,
        p.is_hot,
        p.is_new,
        p.sort_order,
        p.create_time,
        p.update_time
    </sql>

    <!-- 基础查询表连接 -->
    <sql id="BaseJoins">
        FROM t_product p
        LEFT JOIN t_product_category c ON p.category_id = c.id
        LEFT JOIN t_brand b ON p.brand_id = b.id
    </sql>

    <!-- 查询条件 -->
    <sql id="QueryConditions">
        <where>
            p.deleted = 0
            <if test="query.name != null and query.name != ''">
                AND p.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.categoryId != null">
                AND p.category_id = #{query.categoryId}
            </if>
            <if test="query.merchantId != null">
                AND p.merchant_id = #{query.merchantId}
            </if>
            <if test="query.brandId != null">
                AND p.brand_id = #{query.brandId}
            </if>
            <if test="query.minPrice != null">
                AND p.price >= #{query.minPrice}
            </if>
            <if test="query.maxPrice != null">
                AND p.price &lt;= #{query.maxPrice}
            </if>
            <if test="query.status != null">
                AND p.status = #{query.status}
            </if>
            <if test="query.isRecommend != null">
                AND p.is_recommend = #{query.isRecommend}
            </if>
            <if test="query.isHot != null">
                AND p.is_hot = #{query.isHot}
            </if>
            <if test="query.isNew != null">
                AND p.is_new = #{query.isNew}
            </if>
        </where>
    </sql>

    <!-- 排序条件 -->
    <sql id="OrderBy">
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ORDER BY p.${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY p.sort_order ASC, p.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 分页查询商品列表 -->
    <select id="selectProductPage" resultMap="ProductVOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        <include refid="QueryConditions"/>
        <include refid="OrderBy"/>
    </select>

    <!-- 根据ID查询商品详情 -->
    <select id="selectProductById" resultMap="ProductVOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        WHERE p.id = #{id} AND p.deleted = 0
    </select>

</mapper>

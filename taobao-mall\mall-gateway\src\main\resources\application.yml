server:
  port: 8080

spring:
  application:
    name: mall-gateway
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: mall
      config:
        server-addr: localhost:8848
        namespace: mall
        file-extension: yml
        group: DEFAULT_GROUP
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 用户服务路由
        - id: mall-user
          uri: lb://mall-user
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                key-resolver: "#{@ipKeyResolver}"
        
        # 商品服务路由
        - id: mall-product
          uri: lb://mall-product
          predicates:
            - Path=/api/product/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 40
                key-resolver: "#{@ipKeyResolver}"
        
        # 订单服务路由
        - id: mall-order
          uri: lb://mall-order
          predicates:
            - Path=/api/order/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                key-resolver: "#{@ipKeyResolver}"
        
        # 购物车服务路由
        - id: mall-cart
          uri: lb://mall-cart
          predicates:
            - Path=/api/cart/**
          filters:
            - StripPrefix=2
        
        # 商家服务路由
        - id: mall-merchant
          uri: lb://mall-merchant
          predicates:
            - Path=/api/merchant/**
          filters:
            - StripPrefix=2
        
        # 搜索服务路由
        - id: mall-search
          uri: lb://mall-search
          predicates:
            - Path=/api/search/**
          filters:
            - StripPrefix=2
        
        # 支付服务路由
        - id: mall-payment
          uri: lb://mall-payment
          predicates:
            - Path=/api/payment/**
          filters:
            - StripPrefix=2
        
        # 管理后台路由
        - id: mall-admin
          uri: lb://mall-admin
          predicates:
            - Path=/api/admin/**
          filters:
            - StripPrefix=2
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# Sentinel配置
spring.cloud.sentinel:
  transport:
    dashboard: localhost:8858
    port: 8719
  datasource:
    ds1:
      nacos:
        server-addr: localhost:8848
        dataId: gateway-flow-rules
        groupId: SENTINEL_GROUP
        rule-type: flow

# 日志配置
logging:
  level:
    com.mall.gateway: debug
    org.springframework.cloud.gateway: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

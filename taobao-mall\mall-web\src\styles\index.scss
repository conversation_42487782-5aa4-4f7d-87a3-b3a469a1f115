// 全局样式

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  background-color: #f5f5f5;
}

// 链接样式
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

// 列表样式
ul, ol {
  list-style: none;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 按钮样式
button {
  border: none;
  outline: none;
  cursor: pointer;
}

// 输入框样式
input, textarea {
  outline: none;
}

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 居中
.text-center {
  text-align: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 间距
.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }

.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }

.ml-10 { margin-left: 10px; }
.ml-20 { margin-left: 20px; }
.ml-30 { margin-left: 30px; }

.mr-10 { margin-right: 10px; }
.mr-20 { margin-right: 20px; }
.mr-30 { margin-right: 30px; }

.pt-10 { padding-top: 10px; }
.pt-20 { padding-top: 20px; }
.pt-30 { padding-top: 30px; }

.pb-10 { padding-bottom: 10px; }
.pb-20 { padding-bottom: 20px; }
.pb-30 { padding-bottom: 30px; }

.pl-10 { padding-left: 10px; }
.pl-20 { padding-left: 20px; }
.pl-30 { padding-left: 30px; }

.pr-10 { padding-right: 10px; }
.pr-20 { padding-right: 20px; }
.pr-30 { padding-right: 30px; }

// 主题色
:root {
  --primary-color: #ff6600;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  --background-base: #f5f7fa;
  --background-light: #fafafa;
}

// 自定义Element Plus样式
.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-button--primary:hover {
  background-color: #ff7f33;
  border-color: #ff7f33;
}

.el-link--primary {
  color: var(--primary-color);
}

.el-link--primary:hover {
  color: #ff7f33;
}

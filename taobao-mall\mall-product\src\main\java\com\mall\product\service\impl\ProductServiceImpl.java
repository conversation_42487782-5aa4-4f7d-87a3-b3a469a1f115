package com.mall.product.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mall.common.dto.PageResult;
import com.mall.common.exception.BusinessException;
import com.mall.common.result.ResultCode;
import com.mall.product.dto.ProductQueryDTO;
import com.mall.product.entity.Product;
import com.mall.product.mapper.ProductMapper;
import com.mall.product.service.ProductService;
import com.mall.product.vo.ProductVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 商品服务实现类
 */
@Slf4j
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {
    
    @Override
    public PageResult<ProductVO> getProductPage(ProductQueryDTO queryDTO) {
        Page<ProductVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<ProductVO> result = baseMapper.selectProductPage(page, queryDTO);
        
        // 处理图片列表
        result.getRecords().forEach(this::processImageList);
        
        return new PageResult<>(
                result.getRecords(),
                result.getTotal(),
                result.getCurrent(),
                result.getSize()
        );
    }
    
    @Override
    public ProductVO getProductById(Long id) {
        if (id == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "商品ID不能为空");
        }
        
        ProductVO productVO = baseMapper.selectProductById(id);
        if (productVO == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_EXIST);
        }
        
        // 处理图片列表
        processImageList(productVO);
        
        return productVO;
    }
    
    @Override
    public List<ProductVO> getRecommendProducts(Integer limit) {
        ProductQueryDTO queryDTO = new ProductQueryDTO();
        queryDTO.setIsRecommend(1);
        queryDTO.setStatus(1);
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(limit != null ? limit : 10);
        queryDTO.setOrderBy("sort_order");
        queryDTO.setOrderDirection("ASC");
        
        return getProductPage(queryDTO).getRecords();
    }
    
    @Override
    public List<ProductVO> getHotProducts(Integer limit) {
        ProductQueryDTO queryDTO = new ProductQueryDTO();
        queryDTO.setIsHot(1);
        queryDTO.setStatus(1);
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(limit != null ? limit : 10);
        queryDTO.setOrderBy("sales");
        queryDTO.setOrderDirection("DESC");
        
        return getProductPage(queryDTO).getRecords();
    }
    
    @Override
    public List<ProductVO> getNewProducts(Integer limit) {
        ProductQueryDTO queryDTO = new ProductQueryDTO();
        queryDTO.setIsNew(1);
        queryDTO.setStatus(1);
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(limit != null ? limit : 10);
        queryDTO.setOrderBy("create_time");
        queryDTO.setOrderDirection("DESC");
        
        return getProductPage(queryDTO).getRecords();
    }
    
    @Override
    public List<ProductVO> getProductsByCategory(Long categoryId, Integer limit) {
        ProductQueryDTO queryDTO = new ProductQueryDTO();
        queryDTO.setCategoryId(categoryId);
        queryDTO.setStatus(1);
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(limit != null ? limit : 20);
        queryDTO.setOrderBy("sort_order");
        queryDTO.setOrderDirection("ASC");
        
        return getProductPage(queryDTO).getRecords();
    }
    
    @Override
    public PageResult<ProductVO> searchProducts(String keyword, ProductQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(keyword)) {
            queryDTO.setName(keyword);
        }
        queryDTO.setStatus(1); // 只搜索上架商品
        
        return getProductPage(queryDTO);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStock(Long productId, Integer quantity) {
        if (productId == null || quantity == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "参数不能为空");
        }
        
        Product product = getById(productId);
        if (product == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_EXIST);
        }
        
        if (product.getStock() + quantity < 0) {
            throw new BusinessException(ResultCode.PRODUCT_STOCK_NOT_ENOUGH);
        }
        
        Product updateProduct = new Product();
        updateProduct.setId(productId);
        updateProduct.setStock(product.getStock() + quantity);
        
        return updateById(updateProduct);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean increaseSales(Long productId, Integer quantity) {
        if (productId == null || quantity == null || quantity <= 0) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "参数错误");
        }
        
        Product product = getById(productId);
        if (product == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_EXIST);
        }
        
        Product updateProduct = new Product();
        updateProduct.setId(productId);
        updateProduct.setSales((product.getSales() != null ? product.getSales() : 0) + quantity);
        
        return updateById(updateProduct);
    }
    
    /**
     * 处理商品图片列表
     */
    private void processImageList(ProductVO productVO) {
        if (StrUtil.isNotBlank(productVO.getImages())) {
            String[] images = productVO.getImages().split(",");
            productVO.setImageList(Arrays.asList(images));
        }
    }
}

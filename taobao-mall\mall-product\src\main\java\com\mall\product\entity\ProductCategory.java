package com.mall.product.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mall.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品分类实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_product_category")
public class ProductCategory extends BaseEntity {
    
    /**
     * 分类名称
     */
    @TableField("name")
    private String name;
    
    /**
     * 父分类ID，0表示一级分类
     */
    @TableField("parent_id")
    private Long parentId;
    
    /**
     * 分类级别 1-一级 2-二级 3-三级
     */
    @TableField("level")
    private Integer level;
    
    /**
     * 分类图标
     */
    @TableField("icon")
    private String icon;
    
    /**
     * 分类图片
     */
    @TableField("image")
    private String image;
    
    /**
     * 分类描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 排序值
     */
    @TableField("sort_order")
    private Integer sortOrder;
    
    /**
     * 状态 0-禁用 1-启用
     */
    @TableField("status")
    private Integer status;
}

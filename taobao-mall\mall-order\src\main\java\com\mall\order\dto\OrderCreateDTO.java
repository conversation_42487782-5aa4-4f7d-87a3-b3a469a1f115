package com.mall.order.dto;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单创建DTO
 */
@Data
public class OrderCreateDTO {
    
    /**
     * 商家ID
     */
    @NotNull(message = "商家ID不能为空")
    private Long merchantId;
    
    /**
     * 订单商品列表
     */
    @NotEmpty(message = "订单商品不能为空")
    @Valid
    private List<OrderItemDTO> items;
    
    /**
     * 收货地址ID
     */
    @NotNull(message = "收货地址不能为空")
    private Long addressId;
    
    /**
     * 支付方式 1-支付宝 2-微信 3-银联
     */
    @NotNull(message = "支付方式不能为空")
    private Integer payType;
    
    /**
     * 优惠券ID
     */
    private Long couponId;
    
    /**
     * 订单备注
     */
    private String remark;
    
    /**
     * 订单商品DTO
     */
    @Data
    public static class OrderItemDTO {
        
        /**
         * 商品ID
         */
        @NotNull(message = "商品ID不能为空")
        private Long productId;
        
        /**
         * 购买数量
         */
        @NotNull(message = "购买数量不能为空")
        private Integer quantity;
    }
}

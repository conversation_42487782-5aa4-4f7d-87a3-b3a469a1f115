package com.mall.product.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mall.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_product")
public class Product extends BaseEntity {
    
    /**
     * 商品名称
     */
    @TableField("name")
    private String name;
    
    /**
     * 商品副标题
     */
    @TableField("subtitle")
    private String subtitle;
    
    /**
     * 商品描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 商品分类ID
     */
    @TableField("category_id")
    private Long categoryId;
    
    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;
    
    /**
     * 品牌ID
     */
    @TableField("brand_id")
    private Long brandId;
    
    /**
     * 商品价格
     */
    @TableField("price")
    private BigDecimal price;
    
    /**
     * 市场价格
     */
    @TableField("market_price")
    private BigDecimal marketPrice;
    
    /**
     * 成本价格
     */
    @TableField("cost_price")
    private BigDecimal costPrice;
    
    /**
     * 库存数量
     */
    @TableField("stock")
    private Integer stock;
    
    /**
     * 销量
     */
    @TableField("sales")
    private Integer sales;
    
    /**
     * 商品主图
     */
    @TableField("main_image")
    private String mainImage;
    
    /**
     * 商品图片（多张，逗号分隔）
     */
    @TableField("images")
    private String images;
    
    /**
     * 商品详情
     */
    @TableField("detail")
    private String detail;
    
    /**
     * 商品状态 0-下架 1-上架
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 商品重量（克）
     */
    @TableField("weight")
    private Integer weight;
    
    /**
     * 商品体积（立方厘米）
     */
    @TableField("volume")
    private Integer volume;
    
    /**
     * 是否推荐 0-否 1-是
     */
    @TableField("is_recommend")
    private Integer isRecommend;
    
    /**
     * 是否热销 0-否 1-是
     */
    @TableField("is_hot")
    private Integer isHot;
    
    /**
     * 是否新品 0-否 1-是
     */
    @TableField("is_new")
    private Integer isNew;
    
    /**
     * 排序值
     */
    @TableField("sort_order")
    private Integer sortOrder;
}

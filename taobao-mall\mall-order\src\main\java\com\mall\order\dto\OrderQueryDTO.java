package com.mall.order.dto;

import com.mall.common.dto.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderQueryDTO extends PageQuery {
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 订单状态 0-待付款 1-待发货 2-待收货 3-待评价 4-已完成 5-已取消 6-已退款
     */
    private Integer status;
    
    /**
     * 商家ID
     */
    private Long merchantId;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
}

<template>
  <div class="home">
    <!-- 轮播图 -->
    <section class="banner">
      <el-carousel height="300px" indicator-position="outside">
        <el-carousel-item v-for="item in banners" :key="item.id">
          <img :src="item.image" :alt="item.title" class="banner-image" />
        </el-carousel-item>
      </el-carousel>
    </section>

    <!-- 推荐商品 -->
    <section class="recommend">
      <div class="section-header">
        <h2>推荐商品</h2>
        <router-link to="/category" class="more-link">查看更多 ></router-link>
      </div>
      <div class="product-grid">
        <div
          v-for="product in recommendProducts"
          :key="product.id"
          class="product-card"
          @click="goToProduct(product.id)"
        >
          <div class="product-image">
            <img :src="product.mainImage" :alt="product.name" />
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-price">¥{{ product.price }}</p>
            <p class="product-sales">已售{{ product.sales }}件</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 热销商品 -->
    <section class="hot">
      <div class="section-header">
        <h2>热销商品</h2>
        <router-link to="/hot" class="more-link">查看更多 ></router-link>
      </div>
      <div class="product-grid">
        <div
          v-for="product in hotProducts"
          :key="product.id"
          class="product-card"
          @click="goToProduct(product.id)"
        >
          <div class="product-image">
            <img :src="product.mainImage" :alt="product.name" />
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-price">¥{{ product.price }}</p>
            <p class="product-sales">已售{{ product.sales }}件</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 新品推荐 -->
    <section class="new">
      <div class="section-header">
        <h2>新品推荐</h2>
        <router-link to="/new" class="more-link">查看更多 ></router-link>
      </div>
      <div class="product-grid">
        <div
          v-for="product in newProducts"
          :key="product.id"
          class="product-card"
          @click="goToProduct(product.id)"
        >
          <div class="product-image">
            <img :src="product.mainImage" :alt="product.name" />
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-price">¥{{ product.price }}</p>
            <p class="product-sales">已售{{ product.sales }}件</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getRecommendProducts, getHotProducts, getNewProducts } from '@/api/product'

const router = useRouter()

const banners = ref([
  {
    id: 1,
    title: '轮播图1',
    image: 'https://via.placeholder.com/1200x300/ff6600/ffffff?text=Banner+1'
  },
  {
    id: 2,
    title: '轮播图2',
    image: 'https://via.placeholder.com/1200x300/0066ff/ffffff?text=Banner+2'
  },
  {
    id: 3,
    title: '轮播图3',
    image: 'https://via.placeholder.com/1200x300/00cc66/ffffff?text=Banner+3'
  }
])

const recommendProducts = ref([])
const hotProducts = ref([])
const newProducts = ref([])

const goToProduct = (id) => {
  router.push(`/product/${id}`)
}

const loadRecommendProducts = async () => {
  try {
    const response = await getRecommendProducts(8)
    recommendProducts.value = response.data
  } catch (error) {
    console.error('加载推荐商品失败:', error)
  }
}

const loadHotProducts = async () => {
  try {
    const response = await getHotProducts(8)
    hotProducts.value = response.data
  } catch (error) {
    console.error('加载热销商品失败:', error)
  }
}

const loadNewProducts = async () => {
  try {
    const response = await getNewProducts(8)
    newProducts.value = response.data
  } catch (error) {
    console.error('加载新品失败:', error)
  }
}

onMounted(() => {
  loadRecommendProducts()
  loadHotProducts()
  loadNewProducts()
})
</script>

<style scoped>
.home {
  padding: 0;
}

.banner {
  margin-bottom: 40px;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #ff6600;
}

.section-header h2 {
  color: #333;
  font-size: 24px;
  font-weight: bold;
}

.more-link {
  color: #ff6600;
  text-decoration: none;
  font-size: 14px;
}

.more-link:hover {
  text-decoration: underline;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.product-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.product-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 18px;
  font-weight: bold;
  color: #ff6600;
  margin-bottom: 5px;
}

.product-sales {
  font-size: 12px;
  color: #999;
}

.recommend,
.hot,
.new {
  margin-bottom: 40px;
}
</style>

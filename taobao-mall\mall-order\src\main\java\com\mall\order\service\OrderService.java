package com.mall.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mall.common.dto.PageResult;
import com.mall.order.dto.OrderCreateDTO;
import com.mall.order.dto.OrderQueryDTO;
import com.mall.order.entity.Order;
import com.mall.order.vo.OrderVO;

/**
 * 订单服务接口
 */
public interface OrderService extends IService<Order> {
    
    /**
     * 创建订单
     */
    OrderVO createOrder(Long userId, OrderCreateDTO createDTO);
    
    /**
     * 支付订单
     */
    boolean payOrder(Long userId, String orderNo, Integer payType);
    
    /**
     * 取消订单
     */
    boolean cancelOrder(Long userId, String orderNo);
    
    /**
     * 确认收货
     */
    boolean confirmReceive(Long userId, String orderNo);
    
    /**
     * 分页查询用户订单
     */
    PageResult<OrderVO> getUserOrders(Long userId, OrderQueryDTO queryDTO);
    
    /**
     * 根据订单号查询订单详情
     */
    OrderVO getOrderByNo(String orderNo);
    
    /**
     * 根据订单号查询订单详情（用户权限校验）
     */
    OrderVO getUserOrderByNo(Long userId, String orderNo);
    
    /**
     * 订单超时取消处理
     */
    void handleOrderTimeout(String orderNo);
    
    /**
     * 订单自动确认收货处理
     */
    void handleAutoConfirmReceive(String orderNo);
}

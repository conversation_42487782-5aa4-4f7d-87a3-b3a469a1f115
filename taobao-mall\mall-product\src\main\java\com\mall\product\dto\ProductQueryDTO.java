package com.mall.product.dto;

import com.mall.common.dto.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductQueryDTO extends PageQuery {
    
    /**
     * 商品名称（模糊查询）
     */
    private String name;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 商家ID
     */
    private Long merchantId;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 最低价格
     */
    private BigDecimal minPrice;
    
    /**
     * 最高价格
     */
    private BigDecimal maxPrice;
    
    /**
     * 商品状态 0-下架 1-上架
     */
    private Integer status;
    
    /**
     * 是否推荐 0-否 1-是
     */
    private Integer isRecommend;
    
    /**
     * 是否热销 0-否 1-是
     */
    private Integer isHot;
    
    /**
     * 是否新品 0-否 1-是
     */
    private Integer isNew;
}

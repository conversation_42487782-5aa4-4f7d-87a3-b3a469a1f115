package com.mall.order.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mall.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_order")
public class Order extends BaseEntity {
    
    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;
    
    /**
     * 订单状态 0-待付款 1-待发货 2-待收货 3-待评价 4-已完成 5-已取消 6-已退款
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 订单类型 0-普通订单 1-秒杀订单 2-团购订单
     */
    @TableField("order_type")
    private Integer orderType;
    
    /**
     * 商品总金额
     */
    @TableField("product_amount")
    private BigDecimal productAmount;
    
    /**
     * 运费
     */
    @TableField("freight_amount")
    private BigDecimal freightAmount;
    
    /**
     * 优惠金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;
    
    /**
     * 实付金额
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;
    
    /**
     * 支付方式 1-支付宝 2-微信 3-银联
     */
    @TableField("pay_type")
    private Integer payType;
    
    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;
    
    /**
     * 发货时间
     */
    @TableField("ship_time")
    private LocalDateTime shipTime;
    
    /**
     * 收货时间
     */
    @TableField("receive_time")
    private LocalDateTime receiveTime;
    
    /**
     * 完成时间
     */
    @TableField("finish_time")
    private LocalDateTime finishTime;
    
    /**
     * 取消时间
     */
    @TableField("cancel_time")
    private LocalDateTime cancelTime;
    
    /**
     * 收货人姓名
     */
    @TableField("receiver_name")
    private String receiverName;
    
    /**
     * 收货人电话
     */
    @TableField("receiver_phone")
    private String receiverPhone;
    
    /**
     * 收货地址
     */
    @TableField("receiver_address")
    private String receiverAddress;
    
    /**
     * 物流公司
     */
    @TableField("logistics_company")
    private String logisticsCompany;
    
    /**
     * 物流单号
     */
    @TableField("logistics_no")
    private String logisticsNo;
    
    /**
     * 订单备注
     */
    @TableField("remark")
    private String remark;
}

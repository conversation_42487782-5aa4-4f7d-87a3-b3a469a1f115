package com.mall.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis Plus配置
 */
@Configuration
public class MybatisPlusConfig {
    
    /**
     * MyBatis Plus拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        
        return interceptor;
    }
    
    /**
     * 自动填充处理器
     */
    @Component
    public static class MyMetaObjectHandler implements MetaObjectHandler {
        
        @Override
        public void insertFill(MetaObject metaObject) {
            LocalDateTime now = LocalDateTime.now();
            
            // 创建时间
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
            // 更新时间
            this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
            // 逻辑删除标识
            this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
            // 版本号
            this.strictInsertFill(metaObject, "version", Integer.class, 1);
            
            // TODO: 从当前登录用户获取用户ID
            Long currentUserId = getCurrentUserId();
            if (currentUserId != null) {
                this.strictInsertFill(metaObject, "createBy", Long.class, currentUserId);
                this.strictInsertFill(metaObject, "updateBy", Long.class, currentUserId);
            }
        }
        
        @Override
        public void updateFill(MetaObject metaObject) {
            // 更新时间
            this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            
            // TODO: 从当前登录用户获取用户ID
            Long currentUserId = getCurrentUserId();
            if (currentUserId != null) {
                this.strictUpdateFill(metaObject, "updateBy", Long.class, currentUserId);
            }
        }
        
        /**
         * 获取当前登录用户ID
         * TODO: 实现获取当前登录用户的逻辑
         */
        private Long getCurrentUserId() {
            // 这里可以从ThreadLocal、Spring Security Context等获取当前用户信息
            return null;
        }
    }
}

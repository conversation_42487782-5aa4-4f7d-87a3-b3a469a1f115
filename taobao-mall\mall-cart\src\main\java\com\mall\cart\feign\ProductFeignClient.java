package com.mall.cart.feign;

import com.mall.common.result.Result;
import com.mall.cart.feign.fallback.ProductFeignFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 商品服务Feign客户端
 */
@FeignClient(name = "mall-product", fallback = ProductFeignFallback.class)
public interface ProductFeignClient {
    
    /**
     * 根据ID查询商品信息
     */
    @GetMapping("/product/detail/{id}")
    Result<ProductInfo> getProductById(@PathVariable("id") Long id);
    
    /**
     * 商品信息DTO（简化版）
     */
    class ProductInfo {
        private Long id;
        private String name;
        private String mainImage;
        private java.math.BigDecimal price;
        private Integer stock;
        private Integer status;
        
        // getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getMainImage() { return mainImage; }
        public void setMainImage(String mainImage) { this.mainImage = mainImage; }
        
        public java.math.BigDecimal getPrice() { return price; }
        public void setPrice(java.math.BigDecimal price) { this.price = price; }
        
        public Integer getStock() { return stock; }
        public void setStock(Integer stock) { this.stock = stock; }
        
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
    }
}

server:
  port: 8088

spring:
  application:
    name: mall-cart
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: mall
      config:
        server-addr: localhost:8848
        namespace: mall
        file-extension: yml
        group: DEFAULT_GROUP

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 4
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# Feign配置
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000

# 日志配置
logging:
  level:
    com.mall.cart: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

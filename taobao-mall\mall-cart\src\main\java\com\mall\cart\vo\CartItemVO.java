package com.mall.cart.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 购物车商品VO
 */
@Data
public class CartItemVO {
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品图片
     */
    private String productImage;
    
    /**
     * 商品价格
     */
    private BigDecimal productPrice;
    
    /**
     * 数量
     */
    private Integer quantity;
    
    /**
     * 小计金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 是否选中
     */
    private Boolean selected;
    
    /**
     * 商品状态 0-下架 1-上架
     */
    private Integer productStatus;
    
    /**
     * 库存数量
     */
    private Integer stock;
}

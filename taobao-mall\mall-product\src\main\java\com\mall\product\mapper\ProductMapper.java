package com.mall.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mall.product.dto.ProductQueryDTO;
import com.mall.product.entity.Product;
import com.mall.product.vo.ProductVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商品Mapper
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    
    /**
     * 分页查询商品列表（带关联信息）
     */
    IPage<ProductVO> selectProductPage(Page<ProductVO> page, @Param("query") ProductQueryDTO query);
    
    /**
     * 根据ID查询商品详情（带关联信息）
     */
    ProductVO selectProductById(@Param("id") Long id);
}

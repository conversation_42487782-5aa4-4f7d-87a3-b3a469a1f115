# 淘宝商城系统

基于Spring Cloud Alibaba + Vue的微服务电商系统

## 🏗️ 系统架构

### 技术栈

**后端技术栈：**
- Spring Boot 3.2.0
- Spring Cloud 2023.0.0
- Spring Cloud Alibaba 2022.0.0.0
- Nacos (注册中心 + 配置中心)
- Spring Cloud Gateway (网关)
- Sentinel (熔断降级)
- MyBatis Plus (ORM框架)
- MySQL 8.0 (主数据库)
- Redis (缓存)
- Elasticsearch (搜索引擎)
- RocketMQ (消息队列)
- Seata (分布式事务)

**前端技术栈：**
- Vue 3 + Vite
- Element Plus (用户端)
- Ant Design Vue (管理端)

### 微服务模块

```
taobao-mall/
├── mall-common/          # 公共模块
├── mall-gateway/         # 网关服务 (8080)
├── mall-auth/           # 认证授权服务 (8082)
├── mall-user/           # 用户服务 (8081)
├── mall-product/        # 商品服务 (8083)
├── mall-order/          # 订单服务 (8084)
├── mall-payment/        # 支付服务 (8085)
├── mall-merchant/       # 商家服务 (8086)
├── mall-search/         # 搜索服务 (8087)
├── mall-cart/           # 购物车服务 (8088)
├── mall-inventory/      # 库存服务 (8089)
├── mall-logistics/      # 物流服务 (8090)
├── mall-message/        # 消息服务 (8091)
├── mall-admin/          # 管理后台服务 (8092)
└── sql/                 # 数据库脚本
```

## 🚀 快速开始

### 环境要求

- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Node.js 16+ (前端开发)

### 基础环境搭建

#### 1. 安装Nacos
```bash
# 下载Nacos 2.2.3
wget https://github.com/alibaba/nacos/releases/download/2.2.3/nacos-server-2.2.3.tar.gz
tar -xzf nacos-server-2.2.3.tar.gz
cd nacos/bin

# 单机模式启动
sh startup.sh -m standalone

# 访问控制台: http://localhost:8848/nacos
# 默认账号密码: nacos/nacos
```

#### 2. 安装Sentinel
```bash
# 下载Sentinel Dashboard
wget https://github.com/alibaba/Sentinel/releases/download/1.8.6/sentinel-dashboard-1.8.6.jar

# 启动Sentinel Dashboard
java -Dserver.port=8858 -jar sentinel-dashboard-1.8.6.jar

# 访问控制台: http://localhost:8858
# 默认账号密码: sentinel/sentinel
```

#### 3. 数据库初始化
```sql
-- 执行数据库脚本
source sql/mall_user.sql
-- 后续会添加其他服务的数据库脚本
```

### 服务启动顺序

1. **启动基础设施**
   - Nacos (注册中心 + 配置中心)
   - Redis (缓存)
   - MySQL (数据库)
   - Sentinel (可选，用于监控)

2. **启动微服务**
   ```bash
   # 1. 启动网关服务
   cd mall-gateway
   mvn spring-boot:run

   # 2. 启动用户服务
   cd mall-user
   mvn spring-boot:run

   # 3. 启动商品服务
   cd mall-product
   mvn spring-boot:run

   # 4. 启动订单服务
   cd mall-order
   mvn spring-boot:run

   # 5. 启动购物车服务
   cd mall-cart
   mvn spring-boot:run
   ```

3. **启动前端项目**
   ```bash
   cd mall-web
   npm install
   npm run dev
   ```

### 访问地址

启动服务后，可以通过以下地址访问：

**前端应用：**
- 用户端: http://localhost:3000

**API文档：**
- 网关Swagger: http://localhost:8080/doc.html
- 用户服务Swagger: http://localhost:8081/doc.html
- 商品服务Swagger: http://localhost:8083/doc.html
- 订单服务Swagger: http://localhost:8084/doc.html
- 购物车服务Swagger: http://localhost:8088/doc.html

**监控面板：**
- Nacos控制台: http://localhost:8848/nacos (nacos/nacos)
- Sentinel控制台: http://localhost:8858 (sentinel/sentinel)

### 测试接口

#### 用户注册
```bash
curl -X POST http://localhost:8080/api/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser123",
    "password": "123456",
    "confirmPassword": "123456",
    "nickname": "测试用户",
    "phone": "13800138888",
    "email": "<EMAIL>",
    "captcha": "1234",
    "captchaKey": "test-key"
  }'
```

#### 用户登录
```bash
curl -X POST http://localhost:8080/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser123",
    "password": "123456"
  }'
```

## 📊 数据库设计

### 用户服务数据库 (mall_user)
- t_user: 用户基本信息表
- t_user_address: 用户地址表

### 商品服务数据库 (mall_product)
- t_product: 商品基本信息表
- t_product_category: 商品分类表
- t_product_sku: 商品SKU表
- t_product_attribute: 商品属性表

### 订单服务数据库 (mall_order)
- t_order: 订单主表
- t_order_item: 订单明细表
- t_order_log: 订单日志表

## 🔧 开发指南

### 代码规范
- 使用统一的代码格式化配置
- 遵循阿里巴巴Java开发手册
- 接口需要添加Swagger注解
- 异常处理使用统一的业务异常类

### 分支管理
- master: 主分支，用于生产环境
- develop: 开发分支
- feature/*: 功能分支
- hotfix/*: 热修复分支

## 📝 TODO

- [ ] 完善商品服务
- [ ] 完善订单服务
- [ ] 完善支付服务
- [ ] 添加搜索功能
- [ ] 添加消息队列
- [ ] 添加分布式事务
- [ ] 前端Vue项目开发
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能测试

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 📄 许可证

MIT License

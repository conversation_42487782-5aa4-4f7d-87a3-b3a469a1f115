{"name": "mall-web", "version": "1.0.0", "description": "淘宝商城前端项目", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "element-plus": "^2.4.2", "@element-plus/icons-vue": "^2.1.0", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.1", "vite": "^4.5.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.3", "sass": "^1.69.5", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2"}}
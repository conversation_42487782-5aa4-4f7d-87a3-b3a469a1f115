package com.mall.cart.feign.fallback;

import com.mall.common.result.Result;
import com.mall.common.result.ResultCode;
import com.mall.cart.feign.ProductFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 商品服务Feign降级处理
 */
@Slf4j
@Component
public class ProductFeignFallback implements ProductFeignClient {
    
    @Override
    public Result<ProductInfo> getProductById(Long id) {
        log.error("调用商品服务查询商品信息失败，商品ID: {}", id);
        return Result.error(ResultCode.ERROR, "商品服务暂时不可用");
    }
}
